import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
import time

# App configuration
APP_VERSION = "2.0.0"
LAST_UPDATED = "December 2024"

# Performance tracking
start_time = time.time()

# Set page config with enhanced settings
st.set_page_config(
    page_title="Mobile Money Dashboard | Advanced Analytics",
    page_icon="💰",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'https://github.com/streamlit/streamlit',
        'Report a bug': 'https://github.com/streamlit/streamlit/issues',
        'About': f"Mobile Money Transaction Dashboard v{APP_VERSION} - Advanced analytics and fraud detection for mobile money transactions."
    }
)

# Define enhanced color scheme for fraud analysis
FRAUD_COLORS = {
    'normal': '#3b82f6',      # Modern blue for legitimate transactions
    'fraud': '#ef4444',       # Modern red for fraudulent transactions
    'normal_name': 'Legitimate',
    'fraud_name': 'Fraudulent',
    'accent': '#8b5cf6',      # Purple accent
    'success': '#10b981',     # Green for success
    'warning': '#f59e0b',     # Amber for warnings
    'info': '#06b6d4'         # Cyan for info
}

# Enhanced color palette for charts
CHART_COLORS = [
    '#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe',
    '#43e97b', '#38f9d7', '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
]

# Enhanced Custom CSS with Google Fonts and Professional Styling
st.markdown("""
<style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* Global Styles */
.stApp {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Main Header */
.main-header {
    font-family: 'Inter', sans-serif;
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    margin-bottom: 1.5rem;
    letter-spacing: -0.02em;
    line-height: 1.2;
}

/* Subtitle */
.main-subtitle {
    font-family: 'Inter', sans-serif;
    font-size: 1.1rem;
    font-weight: 400;
    color: #64748b;
    text-align: center;
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Section Headers */
.section-header {
    font-family: 'Inter', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin: 2rem 0 1rem 0;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 0.5rem;
}

/* Metric Cards Enhancement */
div[data-testid="metric-container"] {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease-in-out;
}

div[data-testid="metric-container"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Sidebar Styling */
.css-1d391kg {
    background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
    border-right: 1px solid #e2e8f0;
}

/* Insight Boxes */
.insight-box {
    background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
    border: 1px solid #bbf7d0;
    border-left: 4px solid #10b981;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    font-family: 'Inter', sans-serif;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.warning-box {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    border: 1px solid #fde68a;
    border-left: 4px solid #f59e0b;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    font-family: 'Inter', sans-serif;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.error-box {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border: 1px solid #fecaca;
    border-left: 4px solid #ef4444;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    font-family: 'Inter', sans-serif;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Chart Containers */
.js-plotly-plot {
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Button Styling */
.stButton > button {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    border-radius: 8px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease-in-out;
}

.stButton > button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Selectbox and Input Styling */
.stSelectbox > div > div {
    font-family: 'Inter', sans-serif;
    border-radius: 8px;
}

.stTextInput > div > div > input {
    font-family: 'Inter', sans-serif;
    border-radius: 8px;
}

/* Dataframe Styling */
.dataframe {
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.9rem;
}

/* Footer */
.footer {
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    color: #64748b;
    text-align: center;
    padding: 2rem 0;
    border-top: 1px solid #e2e8f0;
    margin-top: 3rem;
}

/* Loading Spinner */
.stSpinner {
    color: #667eea;
}

/* Expander Styling */
.streamlit-expanderHeader {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-radius: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-header {
        font-size: 2rem;
    }

    .section-header {
        font-size: 1.2rem;
    }

    div[data-testid="metric-container"] {
        padding: 1rem;
    }

    .js-plotly-plot {
        height: 300px !important;
    }
}

/* Smooth transitions */
* {
    transition: all 0.2s ease-in-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
</style>
""", unsafe_allow_html=True)

# Load data with enhanced error handling
@st.cache_data(ttl=3600, show_spinner="Loading transaction data...")
def load_data():
    """Load and cache transaction data with proper error handling"""
    try:
        df = pd.read_csv('data/cleaned_transactions.csv')

        # Ensure proper data types for performance
        if 'type' in df.columns:
            df['type'] = df['type'].astype('category')
        if 'isFraud' in df.columns:
            df['isFraud'] = df['isFraud'].astype('int8')

        return df
    except FileNotFoundError:
        st.error("📁 **Data file not found!**")
        st.info("Please run the Week 1 notebook first to generate the cleaned dataset.")
        st.markdown("**Steps to fix:**")
        st.markdown("1. Open `notebooks/Week1_Data_Loading_and_Cleaning.ipynb`")
        st.markdown("2. Run all cells to generate `data/cleaned_transactions.csv`")
        st.markdown("3. Refresh this dashboard")
        st.stop()
    except Exception as e:
        st.error(f"Error loading data: {str(e)}")
        st.stop()

# Load the data with enhanced loading experience
with st.spinner("🚀 Initializing dashboard and loading transaction data..."):
    df = load_data()

# Add a success message
if len(df) > 0:
    st.success(f"✅ Successfully loaded {len(df):,} transactions! Dashboard is ready.", icon="🎉")

# Display data info in sidebar with enhanced styling
st.sidebar.markdown(f"""
<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 1.5rem; border-radius: 12px; margin-bottom: 1.5rem;">
<h3 style="color: white; margin-bottom: 1rem; font-family: 'Inter', sans-serif;">📊 Dataset Overview</h3>
<div style="color: white; font-family: 'Inter', sans-serif; line-height: 1.8;">
<p><strong>Total Transactions:</strong> {len(df):,}</p>
<p><strong>Time Range:</strong> {df['step'].min()} - {df['step'].max()} hours</p>
<p><strong>Transaction Types:</strong> {df['type'].nunique()}</p>
<p><strong>Fraud Rate:</strong> {df['isFraud'].mean()*100:.3f}%</p>
</div>
</div>
""", unsafe_allow_html=True)

# Enhanced sidebar filters with better styling
st.sidebar.markdown("""
<h3 style="color: #1e293b; font-family: 'Inter', sans-serif; margin: 1.5rem 0 1rem 0; padding-bottom: 0.5rem; border-bottom: 2px solid #e2e8f0;">
🎛️ Dashboard Filters
</h3>
""", unsafe_allow_html=True)

# Transaction type filter with better UX
st.sidebar.markdown("**Transaction Types**")
transaction_types = ['All'] + sorted(list(df['type'].unique()))
selected_type = st.sidebar.selectbox(
    'Select transaction type:',
    transaction_types,
    help="Filter transactions by type"
)

# Fraud filter with better labels
st.sidebar.markdown("**Fraud Analysis**")
fraud_filter = st.sidebar.radio(
    'Transaction status:',
    ['All Transactions', 'Fraudulent Only', 'Legitimate Only'],
    help="Filter by fraud status"
)

# Amount range filter with better formatting
st.sidebar.markdown("**Amount Range**")
min_amount = float(df['amount'].min())
max_amount = float(df['amount'].max())

# Use log scale for better UX with large ranges
amount_range = st.sidebar.slider(
    'Transaction amount ($):',
    min_value=min_amount,
    max_value=max_amount,
    value=(min_amount, max_amount),
    format="$%.2f",
    help="Filter transactions by amount range"
)

# Time range filter
st.sidebar.markdown("**Time Period**")
time_range = st.sidebar.slider(
    'Time range (hours):',
    min_value=int(df['step'].min()),
    max_value=int(df['step'].max()),
    value=(int(df['step'].min()), int(df['step'].max())),
    help="Filter by time period in simulation"
)

# Enhanced data filtering with progress indication
@st.cache_data(ttl=300)
def apply_filters(df, selected_type, fraud_filter, amount_range, time_range):
    """Apply filters to dataframe with caching for performance"""
    filtered_df = df.copy()

    # Transaction type filter
    if selected_type != 'All':
        filtered_df = filtered_df[filtered_df['type'] == selected_type]

    # Fraud filter
    if fraud_filter == 'Fraudulent Only':
        filtered_df = filtered_df[filtered_df['isFraud'] == 1]
    elif fraud_filter == 'Legitimate Only':
        filtered_df = filtered_df[filtered_df['isFraud'] == 0]

    # Amount range filter
    filtered_df = filtered_df[
        (filtered_df['amount'] >= amount_range[0]) &
        (filtered_df['amount'] <= amount_range[1])
    ]

    # Time range filter
    filtered_df = filtered_df[
        (filtered_df['step'] >= time_range[0]) &
        (filtered_df['step'] <= time_range[1])
    ]

    return filtered_df

# Apply filters with progress indication
with st.spinner("🔄 Applying filters and processing data..."):
    filtered_df = apply_filters(df, selected_type, fraud_filter, amount_range, time_range)

# Show filter results with enhanced feedback
if len(filtered_df) == 0:
    st.markdown("""
    <div class="warning-box">
    <h4 style="color: #f59e0b; margin-bottom: 1rem;">⚠️ No Results Found</h4>
    <p>No transactions match your current filter selection. Try:</p>
    <ul>
    <li>Expanding the amount range</li>
    <li>Selecting "All Transactions" for fraud filter</li>
    <li>Choosing "All" for transaction type</li>
    <li>Adjusting the time range</li>
    </ul>
    </div>
    """, unsafe_allow_html=True)
    st.stop()
else:
    # Show filter summary
    filter_summary = []
    if selected_type != 'All':
        filter_summary.append(f"Type: {selected_type}")
    if fraud_filter != 'All Transactions':
        filter_summary.append(f"Status: {fraud_filter}")
    if amount_range != (float(df['amount'].min()), float(df['amount'].max())):
        filter_summary.append(f"Amount: ${amount_range[0]:,.0f} - ${amount_range[1]:,.0f}")

    if filter_summary:
        st.info(f"🔍 **Active Filters:** {' | '.join(filter_summary)} | **Results:** {len(filtered_df):,} transactions")

# Main dashboard with enhanced header
st.markdown('<h1 class="main-header">💰 Mobile Money Transaction Dashboard</h1>', unsafe_allow_html=True)
st.markdown('<p class="main-subtitle">Advanced analytics and fraud detection for mobile money transactions with real-time insights and pattern analysis</p>', unsafe_allow_html=True)

# Quick stats banner
col1, col2, col3, col4 = st.columns(4)
with col1:
    st.markdown(f"""
    <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); border-radius: 12px; color: white;">
    <h3 style="margin: 0; font-size: 1.5rem;">{len(filtered_df):,}</h3>
    <p style="margin: 0; font-size: 0.9rem; opacity: 0.9;">Transactions</p>
    </div>
    """, unsafe_allow_html=True)

with col2:
    fraud_count = filtered_df['isFraud'].sum()
    st.markdown(f"""
    <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); border-radius: 12px; color: white;">
    <h3 style="margin: 0; font-size: 1.5rem;">{fraud_count:,}</h3>
    <p style="margin: 0; font-size: 0.9rem; opacity: 0.9;">Fraudulent</p>
    </div>
    """, unsafe_allow_html=True)

with col3:
    total_volume = filtered_df['amount'].sum()
    st.markdown(f"""
    <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 12px; color: white;">
    <h3 style="margin: 0; font-size: 1.5rem;">${total_volume/1e6:.1f}M</h3>
    <p style="margin: 0; font-size: 0.9rem; opacity: 0.9;">Total Volume</p>
    </div>
    """, unsafe_allow_html=True)

with col4:
    fraud_rate = filtered_df['isFraud'].mean() * 100 if len(filtered_df) > 0 else 0
    st.markdown(f"""
    <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); border-radius: 12px; color: white;">
    <h3 style="margin: 0; font-size: 1.5rem;">{fraud_rate:.2f}%</h3>
    <p style="margin: 0; font-size: 0.9rem; opacity: 0.9;">Fraud Rate</p>
    </div>
    """, unsafe_allow_html=True)

# Add a professional divider
st.markdown("""
<div style="height: 2px; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); margin: 2rem 0; border-radius: 1px;"></div>
""", unsafe_allow_html=True)

# Enhanced key metrics with delta calculations
st.markdown('<h2 class="section-header">📊 Key Performance Indicators</h2>', unsafe_allow_html=True)

# Calculate baseline metrics for comparison
baseline_fraud_rate = df['isFraud'].mean() * 100
baseline_avg_amount = df['amount'].mean()

col1, col2, col3, col4 = st.columns(4)

with col1:
    total_transactions = len(filtered_df)
    total_baseline = len(df)
    delta_transactions = total_transactions - total_baseline

    st.metric(
        "Total Transactions",
        f"{total_transactions:,}",
        delta=f"{delta_transactions:,}" if delta_transactions != 0 else None,
        help="Number of transactions matching current filters"
    )

with col2:
    total_volume = filtered_df['amount'].sum()
    st.metric(
        "Total Volume",
        f"${total_volume:,.0f}",
        help="Total transaction volume in dollars"
    )

with col3:
    fraud_rate = filtered_df['isFraud'].mean() * 100 if len(filtered_df) > 0 else 0
    delta_fraud = fraud_rate - baseline_fraud_rate

    st.metric(
        "Fraud Rate",
        f"{fraud_rate:.3f}%",
        delta=f"{delta_fraud:+.3f}%" if abs(delta_fraud) > 0.001 else None,
        delta_color="inverse",
        help="Percentage of fraudulent transactions"
    )

with col4:
    avg_amount = filtered_df['amount'].mean() if len(filtered_df) > 0 else 0
    delta_avg = avg_amount - baseline_avg_amount

    st.metric(
        "Average Amount",
        f"${avg_amount:,.2f}",
        delta=f"${delta_avg:+,.2f}" if abs(delta_avg) > 1 else None,
        help="Average transaction amount"
    )

# Enhanced transaction patterns section
st.markdown('<h2 class="section-header">📈 Transaction Analysis</h2>', unsafe_allow_html=True)

col1, col2 = st.columns(2)

with col1:
    # Enhanced transaction type distribution with fraud analysis
    type_fraud_analysis = filtered_df.groupby('type').agg({
        'isFraud': ['count', 'sum', 'mean'],
        'amount': 'sum'
    }).round(4)
    type_fraud_analysis.columns = ['Total_Count', 'Fraud_Count', 'Fraud_Rate', 'Total_Volume']
    type_fraud_analysis['Fraud_Rate_Percent'] = type_fraud_analysis['Fraud_Rate'] * 100

    # Create pie chart with business insights
    fig = px.pie(
        values=type_fraud_analysis['Total_Count'].values,
        names=type_fraud_analysis.index,
        title='<b>Transaction Volume by Type</b><br><span style="font-size:12px; color:#64748b;">Hover for detailed fraud insights</span>',
        hole=0.4,
        color_discrete_sequence=CHART_COLORS
    )

    # Add fraud rate information to hover
    fraud_rates = type_fraud_analysis['Fraud_Rate_Percent'].values
    volumes = type_fraud_analysis['Total_Volume'].values

    fig.update_traces(
        textposition='inside',
        textinfo='percent+label',
        hovertemplate="<b>%{label}</b><br>" +
                     "Count: %{value:,}<br>" +
                     "Percentage: %{percent}<br>" +
                     "Fraud Rate: %{customdata[0]:.3f}%<br>" +
                     "Total Volume: $%{customdata[1]:,.0f}<br>" +
                     "<extra></extra>",
        customdata=list(zip(fraud_rates, volumes))
    )

    fig.update_layout(
        showlegend=True,
        height=450,
        title_x=0.5,
        title_font=dict(size=16, family="Inter, sans-serif", color="#1e293b"),
        font=dict(family="Inter, sans-serif", size=12),
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        margin=dict(t=80, b=40, l=40, r=40)
    )

    st.plotly_chart(fig, use_container_width=True)

    # Add insights box
    with st.expander("💡 Transaction Type Insights", expanded=False):
        highest_fraud_type = type_fraud_analysis['Fraud_Rate_Percent'].idxmax()
        highest_fraud_rate = type_fraud_analysis['Fraud_Rate_Percent'].max()
        highest_volume_type = type_fraud_analysis['Total_Volume'].idxmax()

        st.markdown(f"""
        <div class="insight-box">
        <h4 style="color: #10b981; margin-bottom: 1rem;">🎯 Key Findings</h4>
        <ul style="line-height: 1.8;">
        <li><strong>Highest Risk Type:</strong> {highest_fraud_type} ({highest_fraud_rate:.3f}% fraud rate)</li>
        <li><strong>Highest Volume Type:</strong> {highest_volume_type} (${type_fraud_analysis.loc[highest_volume_type, 'Total_Volume']:,.0f})</li>
        <li><strong>Total Transaction Types:</strong> {len(type_fraud_analysis)} different types analyzed</li>
        </ul>

        <h4 style="color: #10b981; margin: 1.5rem 0 1rem 0;">💼 Business Impact</h4>
        <p style="line-height: 1.6;">Focus fraud prevention efforts on <strong>{highest_fraud_type}</strong> transactions while maintaining smooth operations for high-volume <strong>{highest_volume_type}</strong> transactions.</p>
        </div>
        """, unsafe_allow_html=True)

with col2:
    # Enhanced amount distribution with fraud overlay and insights
    fig = px.histogram(
        filtered_df,
        x='amount',
        color='isFraud',
        title='<b>Amount Distribution: Fraud vs Legitimate</b><br><span style="font-size:12px; color:#64748b;">Red = Fraudulent, Blue = Legitimate transactions</span>',
        nbins=50,
        opacity=0.8,
        color_discrete_map={0: FRAUD_COLORS['normal'], 1: FRAUD_COLORS['fraud']},
        labels={'isFraud': 'Transaction Status', 'amount': 'Transaction Amount ($)'}
    )

    fig.update_layout(
        height=450,
        title_x=0.5,
        title_font=dict(size=16, family="Inter, sans-serif", color="#1e293b"),
        font=dict(family="Inter, sans-serif", size=12),
        xaxis_title="Transaction Amount ($)",
        yaxis_title="Number of Transactions",
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        margin=dict(t=80, b=60, l=60, r=40),
        legend=dict(
            orientation="h",
            yanchor="top",
            y=-0.15,
            xanchor="center",
            x=0.5,
            font=dict(size=12),
            bgcolor="rgba(255,255,255,0.9)",
            bordercolor="rgba(0,0,0,0.2)",
            borderwidth=1
        )
    )

    # Update legend labels with consistent naming
    fig.for_each_trace(lambda t: t.update(name=FRAUD_COLORS['normal_name'] if t.name == "0" else FRAUD_COLORS['fraud_name']))

    st.plotly_chart(fig, use_container_width=True)

    # Add meaningful insights about amount patterns
    with st.expander("💡 Amount Pattern Insights"):
        fraud_amounts = filtered_df[filtered_df['isFraud'] == 1]['amount']
        normal_amounts = filtered_df[filtered_df['isFraud'] == 0]['amount']

        if len(fraud_amounts) > 0 and len(normal_amounts) > 0:
            st.markdown(f"""
            **Amount Analysis:**
            - **Fraudulent Transactions**: Average ${fraud_amounts.mean():,.2f} (Median: ${fraud_amounts.median():,.2f})
            - **Legitimate Transactions**: Average ${normal_amounts.mean():,.2f} (Median: ${normal_amounts.median():,.2f})
            - **Risk Pattern**: {'Fraudulent amounts are typically higher' if fraud_amounts.mean() > normal_amounts.mean() else 'Fraudulent amounts are typically lower'} than legitimate ones

            **Business Insight**: {'Monitor high-value transactions more closely' if fraud_amounts.mean() > normal_amounts.mean() else 'Small-amount fraud may indicate account testing or micro-fraud patterns'}
            """)
        else:
            st.info("Insufficient data for amount pattern analysis with current filters.")

# Enhanced time series analysis
st.markdown('<h2 class="section-header">⏰ Temporal Analysis</h2>', unsafe_allow_html=True)

# Create hourly analysis for better insights
filtered_df['hour'] = filtered_df['step'] % 24
hourly_data = filtered_df.groupby('hour').agg({
    'amount': ['sum', 'count'],
    'isFraud': ['sum', 'count']
}).round(2)

hourly_data.columns = ['total_volume', 'transaction_count', 'fraud_count', 'total_count']
hourly_data['fraud_rate'] = (hourly_data['fraud_count'] / hourly_data['total_count'] * 100).fillna(0)
hourly_data = hourly_data.reset_index()

# Create enhanced transaction volume chart (showing fraud transactions more clearly)
fig = go.Figure()

# Add normal transaction volume as area (background)
fig.add_trace(
    go.Scatter(
        x=hourly_data['hour'],
        y=hourly_data['transaction_count'] - hourly_data['fraud_count'],
        fill='tozeroy',
        mode='none',
        name=FRAUD_COLORS['normal_name'],
        fillcolor=FRAUD_COLORS['normal'],
        opacity=0.6,
        hovertemplate="<b>Hour %{x}</b><br>" +
                     "Normal Transactions: %{y:,}<br>" +
                     "<extra></extra>"
    )
)

# Add fraud transactions as line with markers (more visible)
fig.add_trace(
    go.Scatter(
        x=hourly_data['hour'],
        y=hourly_data['fraud_count'],
        mode='lines+markers',
        name=FRAUD_COLORS['fraud_name'],
        line=dict(color=FRAUD_COLORS['fraud'], width=4),
        marker=dict(size=8, color=FRAUD_COLORS['fraud']),
        opacity=1.0,
        hovertemplate="<b>Hour %{x}</b><br>" +
                     "Fraud Transactions: %{y:,}<br>" +
                     "Fraud Rate: %{customdata:.2f}%<br>" +
                     "<extra></extra>",
        customdata=hourly_data['fraud_rate']
    )
)

# Update layout
fig.update_layout(
    title='<b>Hourly Transaction Volume: Normal vs Fraud</b><br><span style="font-size:12px; color:#64748b;">Blue area = Normal transactions, Red line = Fraudulent transactions</span>',
    title_x=0.5,
    title_font=dict(size=16, family="Inter, sans-serif", color="#1e293b"),
    font=dict(family="Inter, sans-serif", size=12),
    xaxis_title="Hour of Day",
    yaxis_title="Number of Transactions",
    hovermode='x unified',
    height=520,
    paper_bgcolor='rgba(0,0,0,0)',
    plot_bgcolor='rgba(0,0,0,0)',
    margin=dict(t=80, b=60, l=60, r=40),
    showlegend=True,
    legend=dict(
        orientation="h",
        yanchor="top",
        y=-0.15,
        xanchor="center",
        x=0.5,
        font=dict(size=12),
        bgcolor="rgba(255,255,255,0.9)",
        bordercolor="rgba(0,0,0,0.2)",
        borderwidth=1
    )
)

fig.update_xaxes(
    tickmode='linear',
    tick0=0,
    dtick=2
)

st.plotly_chart(fig, use_container_width=True)

# Add comprehensive insights box
with st.expander("💡 Temporal Pattern Insights & Business Recommendations"):
    if len(hourly_data) > 0:
        peak_hour = hourly_data.loc[hourly_data['fraud_rate'].idxmax(), 'hour']
        peak_fraud_rate = hourly_data['fraud_rate'].max()
        peak_volume_hour = hourly_data.loc[hourly_data['total_volume'].idxmax(), 'hour']
        low_fraud_hour = hourly_data.loc[hourly_data['fraud_rate'].idxmin(), 'hour']
        low_fraud_rate = hourly_data['fraud_rate'].min()

        # Calculate risk periods
        high_risk_hours = hourly_data[hourly_data['fraud_rate'] > hourly_data['fraud_rate'].mean() + hourly_data['fraud_rate'].std()]

        st.markdown(f"""
        **🚨 Risk Analysis:**
        - **Highest Risk Period**: {peak_hour}:00 ({peak_fraud_rate:.2f}% fraud rate)
        - **Safest Period**: {low_fraud_hour}:00 ({low_fraud_rate:.2f}% fraud rate)
        - **High-Risk Hours**: {len(high_risk_hours)} hours above average risk

        **📊 Business Patterns:**
        - **Peak Activity**: {peak_volume_hour}:00 (highest transaction volume)
        - **Average Fraud Rate**: {hourly_data['fraud_rate'].mean():.3f}%
        - **Risk Variation**: {hourly_data['fraud_rate'].std():.3f}% standard deviation

        **💼 Business Recommendations:**
        - **Enhanced Monitoring**: Increase fraud detection sensitivity during hours {', '.join([f"{h}:00" for h in high_risk_hours['hour'].tolist()])}
        - **Resource Allocation**: Deploy more fraud analysts during peak risk period ({peak_hour}:00)
        - **Customer Communication**: Consider transaction limits or additional verification during high-risk hours
        - **Operational Efficiency**: Use low-risk periods ({low_fraud_hour}:00) for system maintenance
        """)
    else:
        st.info("No temporal data available for analysis with current filters.")

# Add meaningful balance analysis section
st.markdown('<h2 class="section-header">💰 Balance Pattern Analysis</h2>', unsafe_allow_html=True)

# Calculate balance-related metrics
filtered_df['balance_change_orig'] = filtered_df['newbalanceOrig'] - filtered_df['oldbalanceOrg']
filtered_df['balance_change_dest'] = filtered_df['newbalanceDest'] - filtered_df['oldbalanceDest']

col1, col2 = st.columns(2)

with col1:
    # Balance behavior by fraud status
    balance_fraud_comparison = filtered_df.groupby('isFraud').agg({
        'oldbalanceOrg': 'mean',
        'newbalanceOrig': 'mean',
        'oldbalanceDest': 'mean',
        'newbalanceDest': 'mean'
    }).round(2)

    # Create balance comparison chart
    fig = go.Figure()

    categories = ['Origin Before', 'Origin After', 'Destination Before', 'Destination After']
    legitimate_values = [
        balance_fraud_comparison.loc[0, 'oldbalanceOrg'],
        balance_fraud_comparison.loc[0, 'newbalanceOrig'],
        balance_fraud_comparison.loc[0, 'oldbalanceDest'],
        balance_fraud_comparison.loc[0, 'newbalanceDest']
    ]

    if 1 in balance_fraud_comparison.index:
        fraudulent_values = [
            balance_fraud_comparison.loc[1, 'oldbalanceOrg'],
            balance_fraud_comparison.loc[1, 'newbalanceOrig'],
            balance_fraud_comparison.loc[1, 'oldbalanceDest'],
            balance_fraud_comparison.loc[1, 'newbalanceDest']
        ]
    else:
        fraudulent_values = [0, 0, 0, 0]

    fig.add_trace(go.Bar(
        name=FRAUD_COLORS['normal_name'],
        x=categories,
        y=legitimate_values,
        marker_color=FRAUD_COLORS['normal'],
        opacity=0.8
    ))

    fig.add_trace(go.Bar(
        name=FRAUD_COLORS['fraud_name'],
        x=categories,
        y=fraudulent_values,
        marker_color=FRAUD_COLORS['fraud'],
        opacity=0.8
    ))

    fig.update_layout(
        title='<b>Average Account Balances: Fraud vs Legitimate</b><br><span style="font-size:12px; color:#64748b;">Comparing balance patterns across transaction types</span>',
        title_font=dict(size=16, family="Inter, sans-serif", color="#1e293b"),
        font=dict(family="Inter, sans-serif", size=12),
        xaxis_title='Account Balance Type',
        yaxis_title='Average Balance ($)',
        barmode='group',
        height=450,
        title_x=0.5,
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        margin=dict(t=80, b=60, l=60, r=40)
    )

    st.plotly_chart(fig, use_container_width=True)

with col2:
    # Zero balance analysis - this is crucial for fraud detection
    zero_balance_analysis = {
        'Origin Before': (filtered_df['oldbalanceOrg'] == 0).sum(),
        'Origin After': (filtered_df['newbalanceOrig'] == 0).sum(),
        'Destination Before': (filtered_df['oldbalanceDest'] == 0).sum(),
        'Destination After': (filtered_df['newbalanceDest'] == 0).sum()
    }

    # Calculate fraud rates for zero balance accounts
    zero_balance_fraud_rates = {}
    for balance_type, column in [('Origin Before', 'oldbalanceOrg'), ('Origin After', 'newbalanceOrig'),
                                ('Destination Before', 'oldbalanceDest'), ('Destination After', 'newbalanceDest')]:
        zero_balance_mask = filtered_df[column] == 0
        if zero_balance_mask.sum() > 0:
            fraud_rate = filtered_df[zero_balance_mask]['isFraud'].mean() * 100
            zero_balance_fraud_rates[balance_type] = fraud_rate
        else:
            zero_balance_fraud_rates[balance_type] = 0

    fig = go.Figure()

    fig.add_trace(go.Bar(
        name='Zero Balance Count',
        x=list(zero_balance_analysis.keys()),
        y=list(zero_balance_analysis.values()),
        marker_color='#FFA500',
        opacity=0.7,
        yaxis='y'
    ))

    fig.add_trace(go.Scatter(
        name='Fraud Rate (%)',
        x=list(zero_balance_fraud_rates.keys()),
        y=list(zero_balance_fraud_rates.values()),
        mode='lines+markers',
        line=dict(color=FRAUD_COLORS['fraud'], width=3),
        marker=dict(size=10),
        yaxis='y2'
    ))

    fig.update_layout(
        title='<b>Zero Balance Accounts & Fraud Risk</b><br><span style="font-size:12px; color:#64748b;">Orange bars = account count, Red line = fraud rate percentage</span>',
        title_font=dict(size=16, family="Inter, sans-serif", color="#1e293b"),
        font=dict(family="Inter, sans-serif", size=12),
        xaxis_title='Balance Type',
        yaxis=dict(title='Number of Zero Balance Accounts', side='left'),
        yaxis2=dict(title='Fraud Rate (%)', side='right', overlaying='y'),
        height=450,
        title_x=0.5,
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        margin=dict(t=80, b=60, l=60, r=60)
    )

    st.plotly_chart(fig, use_container_width=True)

# Add comprehensive balance insights
with st.expander("💡 Balance Pattern Insights & Fraud Indicators"):
    if len(filtered_df) > 0:
        # Calculate key balance metrics
        total_zero_orig_before = (filtered_df['oldbalanceOrg'] == 0).sum()
        total_zero_orig_after = (filtered_df['newbalanceOrig'] == 0).sum()
        total_zero_dest_before = (filtered_df['oldbalanceDest'] == 0).sum()
        total_zero_dest_after = (filtered_df['newbalanceDest'] == 0).sum()

        # Fraud rates for zero balances
        fraud_rate_zero_orig = filtered_df[filtered_df['oldbalanceOrg'] == 0]['isFraud'].mean() * 100 if total_zero_orig_before > 0 else 0
        fraud_rate_zero_dest = filtered_df[filtered_df['oldbalanceDest'] == 0]['isFraud'].mean() * 100 if total_zero_dest_before > 0 else 0

        st.markdown(f"""
        **🔍 Balance Pattern Analysis:**
        - **Zero Origin Balances**: {total_zero_orig_before:,} accounts ({fraud_rate_zero_orig:.2f}% fraud rate)
        - **Zero Destination Balances**: {total_zero_dest_before:,} accounts ({fraud_rate_zero_dest:.2f}% fraud rate)
        - **Balance Drain Pattern**: {total_zero_orig_after:,} accounts emptied after transaction
        - **New Account Pattern**: {total_zero_dest_after:,} destination accounts with zero final balance

        **🚨 Fraud Risk Indicators:**
        - **High Risk**: Zero balance accounts show {'higher' if max(fraud_rate_zero_orig, fraud_rate_zero_dest) > filtered_df['isFraud'].mean() * 100 else 'similar'} fraud rates
        - **Suspicious Pattern**: {'Account draining behavior detected' if total_zero_orig_after > total_zero_orig_before else 'Normal balance patterns observed'}
        - **Money Mule Indicator**: {'High destination zero balances suggest potential money laundering' if total_zero_dest_after > len(filtered_df) * 0.1 else 'Destination balance patterns appear normal'}

        **💼 Business Recommendations:**
        - **Enhanced KYC**: Implement stricter verification for zero-balance account transactions
        - **Transaction Limits**: Consider lower limits for accounts with zero balances
        - **Real-time Monitoring**: Flag transactions that result in account balance draining
        - **Risk Scoring**: Include balance history in fraud risk calculations
        """)
    else:
        st.info("No balance data available for analysis with current filters.")

# Enhanced transaction details section
st.markdown('<h2 class="section-header">🔍 Transaction Explorer</h2>', unsafe_allow_html=True)

# Add enhanced search functionality
st.markdown("""
<div style="background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%); padding: 1.5rem; border-radius: 12px; border: 1px solid #e2e8f0; margin-bottom: 1.5rem;">
<h4 style="color: #1e293b; margin-bottom: 1rem; font-family: 'Inter', sans-serif;">🔍 Smart Search</h4>
""", unsafe_allow_html=True)

search_term = st.text_input(
    "Search transactions:",
    placeholder="Enter transaction ID, account name, type, or amount...",
    help="Search across multiple fields including transaction IDs, account names, types, and amounts"
)

st.markdown("</div>", unsafe_allow_html=True)

# Apply search filter
display_df = filtered_df.copy()
if search_term:
    # Search across multiple columns
    search_mask = (
        display_df['nameOrig'].astype(str).str.contains(search_term, case=False, na=False) |
        display_df['nameDest'].astype(str).str.contains(search_term, case=False, na=False) |
        display_df['type'].astype(str).str.contains(search_term, case=False, na=False) |
        display_df['amount'].astype(str).str.contains(search_term, case=False, na=False)
    )
    display_df = display_df[search_mask]

# Enhanced pagination controls
col1, col2, col3 = st.columns([1, 1, 2])

with col1:
    rows_per_page = st.selectbox('Rows per page:', [10, 25, 50, 100], index=1)

with col2:
    total_pages = len(display_df) // rows_per_page + (1 if len(display_df) % rows_per_page > 0 else 0)
    if total_pages > 0:
        current_page = st.number_input('Page:', min_value=1, max_value=total_pages, value=1)
    else:
        current_page = 1
        st.write("No results found")

with col3:
    if len(display_df) > 0:
        start_idx = (current_page - 1) * rows_per_page
        end_idx = min(start_idx + rows_per_page, len(display_df))
        st.info(f'📊 Showing {start_idx + 1}-{end_idx} of {len(display_df):,} transactions')
    else:
        st.warning("No transactions match your search criteria")

# Display enhanced dataframe
if len(display_df) > 0:
    # Select and rename columns for better display
    display_columns = {
        'step': 'Time Step',
        'type': 'Type',
        'amount': 'Amount ($)',
        'isFraud': 'Fraud',
        'nameOrig': 'From Account',
        'nameDest': 'To Account',
        'oldbalanceOrg': 'From Balance Before',
        'newbalanceOrig': 'From Balance After',
        'oldbalanceDest': 'To Balance Before',
        'newbalanceDest': 'To Balance After'
    }

    table_df = display_df[list(display_columns.keys())].iloc[start_idx:end_idx].copy()
    table_df = table_df.rename(columns=display_columns)

    # Format the dataframe for better display
    table_df['Amount ($)'] = table_df['Amount ($)'].apply(lambda x: f"${x:,.2f}")
    table_df['Fraud'] = table_df['Fraud'].map({0: '✅ No', 1: '🚨 Yes'})

    # Format balance columns
    for col in ['From Balance Before', 'From Balance After', 'To Balance Before', 'To Balance After']:
        table_df[col] = table_df[col].apply(lambda x: f"${x:,.2f}")

    st.dataframe(
        table_df,
        use_container_width=True,
        height=400
    )

    # Enhanced download options
    col1, col2 = st.columns(2)

    with col1:
        csv = display_df.to_csv(index=False)
        st.download_button(
            label="📥 Download Filtered Data (CSV)",
            data=csv,
            file_name=f"mobile_money_transactions_{len(display_df)}_records.csv",
            mime="text/csv",
            help="Download the currently filtered transaction data"
        )

    with col2:
        # Generate summary report
        summary_report = f"""
Mobile Money Transaction Analysis Report
Generated: {APP_VERSION}

DATASET SUMMARY:
- Total Transactions Analyzed: {len(filtered_df):,}
- Fraudulent Transactions: {filtered_df['isFraud'].sum():,}
- Fraud Rate: {filtered_df['isFraud'].mean()*100:.3f}%
- Total Volume: ${filtered_df['amount'].sum():,.2f}
- Average Transaction: ${filtered_df['amount'].mean():,.2f}

TRANSACTION TYPES:
{filtered_df['type'].value_counts().to_string()}

FRAUD ANALYSIS BY TYPE:
{filtered_df.groupby('type')['isFraud'].agg(['count', 'sum', 'mean']).to_string()}

FILTERS APPLIED:
- Transaction Type: {selected_type}
- Fraud Filter: {fraud_filter}
- Amount Range: ${amount_range[0]:,.2f} - ${amount_range[1]:,.2f}
- Time Range: {time_range[0]} - {time_range[1]} hours
        """

        st.download_button(
            label="📊 Download Analysis Report (TXT)",
            data=summary_report,
            file_name=f"mobile_money_analysis_report_{len(filtered_df)}_transactions.txt",
            mime="text/plain",
            help="Download a comprehensive analysis report"
        )
else:
    st.info("Adjust your filters or search terms to see transaction data.")

# Enhanced Footer with additional information
st.markdown('<h3 class="section-header">📋 Dashboard Information</h3>', unsafe_allow_html=True)

col1, col2 = st.columns(2)

with col1:
    st.markdown("""
    <div style="background: #ffffff; border: 2px solid #10b981; border-radius: 12px; padding: 1.5rem; margin: 1rem 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h4 style="color: #059669; margin-bottom: 1rem; font-family: 'Inter', sans-serif;">🚀 About this Dashboard</h4>
    <ul style="line-height: 1.8; color: #374151; font-family: 'Inter', sans-serif;">
    <li>Built with <strong>Streamlit</strong> and <strong>Plotly</strong> for interactive data exploration</li>
    <li>Data source: <strong>PaySim</strong> synthetic mobile money dataset</li>
    <li>Real-time filtering and analysis capabilities</li>
    <li>Advanced fraud detection insights and pattern analysis</li>
    <li>Professional typography with <strong>Inter</strong> font family</li>
    </ul>
    </div>
    """, unsafe_allow_html=True)

with col2:
    st.markdown("""
    <div style="background: #ffffff; border: 2px solid #f59e0b; border-radius: 12px; padding: 1.5rem; margin: 1rem 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h4 style="color: #d97706; margin-bottom: 1rem; font-family: 'Inter', sans-serif;">📖 How to Use</h4>
    <ol style="line-height: 1.8; color: #374151; font-family: 'Inter', sans-serif;">
    <li>Use the <strong>sidebar filters</strong> to narrow down transactions</li>
    <li>Explore the <strong>visualizations</strong> to understand patterns</li>
    <li>Use the <strong>transaction explorer</strong> to examine individual records</li>
    <li>Download filtered data for further analysis</li>
    <li>Expand insight boxes for detailed business recommendations</li>
    </ol>
    </div>
    """, unsafe_allow_html=True)

# Add help section to sidebar
st.sidebar.markdown("---")
with st.sidebar.expander("❓ Help & Tips"):
    st.markdown("""
    **Quick Tips:**
    - Use Ctrl+F to search within the page
    - Click and drag on charts to zoom
    - Double-click charts to reset zoom
    - Use the search box to find specific transactions
    - Expand insight boxes for detailed analysis

    **Keyboard Shortcuts:**
    - `Ctrl + R` - Refresh dashboard
    - `Ctrl + S` - Save current view
    - `Esc` - Clear search/filters
    """)

# Add performance and version info to sidebar
load_time = time.time() - start_time
st.sidebar.markdown(f"""
<div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px; margin-top: 1rem;">
<p style="font-size: 0.8rem; color: #64748b; margin: 0; font-family: 'Inter', sans-serif;">
<strong>Dashboard v{APP_VERSION}</strong><br>
Last updated: {LAST_UPDATED}<br>
<span style="color: #10b981;">⚡ Loaded in {load_time:.2f}s</span>
</p>
</div>
""", unsafe_allow_html=True)

# Professional footer
st.markdown(f"""
<div class="footer">
<p style="font-size: 1rem; font-weight: 500; margin-bottom: 0.5rem;">
💰 Mobile Money Transaction Dashboard v{APP_VERSION}
</p>
<p style="font-size: 0.9rem; color: #64748b;">
Built with ❤️ using Streamlit • Enhanced with modern design principles • Powered by advanced analytics
</p>
</div>
""", unsafe_allow_html=True)
