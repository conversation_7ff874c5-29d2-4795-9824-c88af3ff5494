{"cells": [{"cell_type": "markdown", "id": "cb326bef", "metadata": {}, "source": ["# Week 1: Mobile Money Transaction Analysis - Data Loading and Cleaning\n", "\n", "This notebook covers Week 1 of our mobile money transaction analysis project. We'll focus on:\n", "1. Loading the dataset\n", "2. Understanding the data structure\n", "3. Cleaning and preprocessing\n", "4. Creating a data dictionary\n", "\n", "## Setup and Initial Data Loading"]}, {"cell_type": "code", "execution_count": 1, "id": "3a995312", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd  # For data manipulation and analysis\n", "import numpy as np   # For numerical operations\n", "import matplotlib.pyplot as plt  # For basic plotting\n", "import seaborn as sns  # For statistical visualizations\n", "import plotly.express as px  # For interactive visualizations\n", "import warnings\n", "\n", "# Set display options for better readability\n", "pd.set_option('display.max_columns', None)\n", "warnings.filterwarnings('ignore')  # Suppress warnings for cleaner output\n", "\n", "# Set visual styles\n", "plt.style.use('default')  # Use default matplotlib style\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (10, 6)  # Set default figure size"]}, {"cell_type": "markdown", "id": "5f6d8ddd", "metadata": {}, "source": ["## Environment Setup Verification\n", "\n", "Before we start, let's verify that we have all the required packages installed. If any package is missing, you can install it using:\n", "```bash\n", "pip install pandas numpy mat<PERSON><PERSON><PERSON><PERSON> seaborn plotly\n", "```\n", "\n", "Let's check our Python environment and package versions:"]}, {"cell_type": "code", "execution_count": 2, "id": "env_check", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python version: 3.13.5 | packaged by Anaconda, Inc. | (main, Jun 12 2025, 16:37:03) [MSC v.1929 64 bit (AMD64)]\n", "Pandas version: 2.2.3\n", "NumPy version: 2.1.3\n", "Matplotlib version: 3.10.0\n", "Seaborn version: 0.13.2\n", "Plotly version: 5.24.1\n", "\n", "All packages loaded successfully!\n"]}], "source": ["# Check package versions\n", "import sys\n", "import plotly\n", "import matplotlib\n", "print(f\"Python version: {sys.version}\")\n", "print(f\"Pandas version: {pd.__version__}\")\n", "print(f\"NumPy version: {np.__version__}\")\n", "print(f\"Matplotlib version: {matplotlib.__version__}\")\n", "print(f\"Seaborn version: {sns.__version__}\")\n", "print(f\"Plotly version: {plotly.__version__}\")\n", "print(\"\\nAll packages loaded successfully!\")"]}, {"cell_type": "markdown", "id": "7eef3029", "metadata": {}, "source": ["## Data Loading\n", "\n", "Now we'll load our dataset. The dataset is a CSV file containing synthetic mobile money transactions from the PaySim dataset. Each row represents a single transaction with various attributes like:\n", "- **step**: time step in the simulation (1 step = 1 hour)\n", "- **type**: transaction type (CASH_IN, CASH_OUT, DEBIT, PAYMENT, TRANSFER)\n", "- **amount**: transaction amount in local currency\n", "- **nameOrig**: sender ID (customer who initiated the transaction)\n", "- **nameDest**: receiver ID (customer who received the transaction)\n", "- **isFraud**: binary fraud label (1 = fraud, 0 = legitimate)\n", "- **oldbalanceOrg**: sender's balance before transaction\n", "- **newbalanceOrig**: sender's balance after transaction\n", "- **oldbalanceDest**: receiver's balance before transaction\n", "- **newbalanceDest**: receiver's balance after transaction"]}, {"cell_type": "code", "execution_count": 3, "id": "e9e98a95", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading dataset from: ../data/PS_20174392719_1491204439457_log.csv\n", "Dataset loaded successfully! Shape: (6362620, 11)\n", "\n", "First few rows of the dataset:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>step</th>\n", "      <th>type</th>\n", "      <th>amount</th>\n", "      <th>name<PERSON>rig</th>\n", "      <th>oldbalanceOrg</th>\n", "      <th>newbalanceOrig</th>\n", "      <th>nameDest</th>\n", "      <th>oldbalanceDest</th>\n", "      <th>newbalanceDest</th>\n", "      <th>is<PERSON><PERSON><PERSON></th>\n", "      <th>isFlaggedFraud</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>PAYMENT</td>\n", "      <td>9839.64</td>\n", "      <td>C1231006815</td>\n", "      <td>170136.0</td>\n", "      <td>160296.36</td>\n", "      <td>M1979787155</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>PAYMENT</td>\n", "      <td>1864.28</td>\n", "      <td>C1666544295</td>\n", "      <td>21249.0</td>\n", "      <td>19384.72</td>\n", "      <td>M2044282225</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>TRANSFER</td>\n", "      <td>181.00</td>\n", "      <td>C1305486145</td>\n", "      <td>181.0</td>\n", "      <td>0.00</td>\n", "      <td>C553264065</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>CASH_OUT</td>\n", "      <td>181.00</td>\n", "      <td>C840083671</td>\n", "      <td>181.0</td>\n", "      <td>0.00</td>\n", "      <td>C38997010</td>\n", "      <td>21182.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>PAYMENT</td>\n", "      <td>11668.14</td>\n", "      <td>C2048537720</td>\n", "      <td>41554.0</td>\n", "      <td>29885.86</td>\n", "      <td>M1230701703</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   step      type    amount     nameOrig  oldbalanceOrg  newbalanceOrig  \\\n", "0     1   PAYMENT   9839.64  C1231006815       170136.0       160296.36   \n", "1     1   PAYMENT   1864.28  C1666544295        21249.0        19384.72   \n", "2     1  TRANSFER    181.00  C1305486145          181.0            0.00   \n", "3     1  CASH_OUT    181.00   C840083671          181.0            0.00   \n", "4     1   PAYMENT  11668.14  C2048537720        41554.0        29885.86   \n", "\n", "      nameDest  oldbalanceDest  newbalanceDest  isFraud  isFlaggedFraud  \n", "0  M1979787155             0.0             0.0        0               0  \n", "1  M2044282225             0.0             0.0        0               0  \n", "2   C553264065             0.0             0.0        1               0  \n", "3    C38997010         21182.0             0.0        1               0  \n", "4  M1230701703             0.0             0.0        0               0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Dataset Information:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 6362620 entries, 0 to 6362619\n", "Data columns (total 11 columns):\n", " #   Column          Dtype  \n", "---  ------          -----  \n", " 0   step            int64  \n", " 1   type            object \n", " 2   amount          float64\n", " 3   nameOrig        object \n", " 4   oldbalanceOrg   float64\n", " 5   newbalanceOrig  float64\n", " 6   nameDest        object \n", " 7   oldbalanceDest  float64\n", " 8   newbalanceDest  float64\n", " 9   is<PERSON><PERSON><PERSON>         int64  \n", " 10  isFlaggedFraud  int64  \n", "dtypes: float64(5), int64(3), object(3)\n", "memory usage: 534.0+ MB\n"]}, {"data": {"text/plain": ["None"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Dataset contains 6,362,620 transactions\n", "Memory usage: 1452.57 MB\n"]}], "source": ["# Load the dataset\n", "import os\n", "\n", "# Try different possible locations for the dataset\n", "possible_paths = [\n", "    '../data/PS_20174392719_1491204439457_log.csv',  # Data directory (primary)\n", "    'data/PS_20174392719_1491204439457_log.csv',     # Data directory (from root)\n", "    '../PS_20174392719_1491204439457_log.csv'        # Parent directory (fallback)\n", "]\n", "\n", "df = None\n", "for path in possible_paths:\n", "    if os.path.exists(path):\n", "        print(f\"Loading dataset from: {path}\")\n", "        df = pd.read_csv(path)\n", "        break\n", "\n", "if df is None:\n", "    print(\"Dataset not found! Please ensure the PaySim dataset CSV file is in the project directory.\")\n", "    print(\"Expected filename: PS_20174392719_1491204439457_log.csv\")\n", "    print(\"You can download it from: https://www.kaggle.com/datasets/ealaxi/paysim1\")\n", "else:\n", "    print(f\"Dataset loaded successfully! Shape: {df.shape}\")\n", "    \n", "    # Display first few rows and basic information about the dataset\n", "    print(\"\\nFirst few rows of the dataset:\")\n", "    display(df.head())\n", "    \n", "    print(\"\\nDataset Information:\")\n", "    display(df.info())\n", "    \n", "    print(f\"\\nDataset contains {len(df):,} transactions\")\n", "    print(f\"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")"]}, {"cell_type": "markdown", "id": "4389970d", "metadata": {}, "source": ["## Data Cleaning and Validation\n", "\n", "Let's check for and handle any data quality issues:\n", "1. Missing values\n", "2. Duplicate transactions\n", "3. Data type consistency\n", "4. Value ranges and outliers"]}, {"cell_type": "code", "execution_count": 4, "id": "23190fc7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in each column:\n"]}, {"data": {"text/plain": ["step              0\n", "type              0\n", "amount            0\n", "nameOrig          0\n", "oldbalanceOrg     0\n", "newbalanceOrig    0\n", "nameDest          0\n", "oldbalanceDest    0\n", "newbalanceDest    0\n", "isFraud           0\n", "isFlaggedFraud    0\n", "dtype: int64"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✓ No missing values found!\n", "\n", "Number of duplicate rows: 0\n", "✓ No duplicate transactions found!\n", "\n", "Data types of each column:\n"]}, {"data": {"text/plain": ["step                int64\n", "type               object\n", "amount            float64\n", "nameOrig           object\n", "oldbalanceOrg     float64\n", "newbalanceOrig    float64\n", "nameDest           object\n", "oldbalanceDest    float64\n", "newbalanceDest    float64\n", "isF<PERSON>ud             int64\n", "isFlaggedFraud      int64\n", "dtype: object"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Basic statistics of numerical columns:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>step</th>\n", "      <th>amount</th>\n", "      <th>oldbalanceOrg</th>\n", "      <th>newbalanceOrig</th>\n", "      <th>oldbalanceDest</th>\n", "      <th>newbalanceDest</th>\n", "      <th>is<PERSON><PERSON><PERSON></th>\n", "      <th>isFlaggedFraud</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>6.362620e+06</td>\n", "      <td>6.362620e+06</td>\n", "      <td>6.362620e+06</td>\n", "      <td>6.362620e+06</td>\n", "      <td>6.362620e+06</td>\n", "      <td>6.362620e+06</td>\n", "      <td>6.362620e+06</td>\n", "      <td>6.362620e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2.433972e+02</td>\n", "      <td>1.798619e+05</td>\n", "      <td>8.338831e+05</td>\n", "      <td>8.551137e+05</td>\n", "      <td>1.100702e+06</td>\n", "      <td>1.224996e+06</td>\n", "      <td>1.290820e-03</td>\n", "      <td>2.514687e-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1.423320e+02</td>\n", "      <td>6.038582e+05</td>\n", "      <td>2.888243e+06</td>\n", "      <td>2.924049e+06</td>\n", "      <td>3.399180e+06</td>\n", "      <td>3.674129e+06</td>\n", "      <td>3.590480e-02</td>\n", "      <td>1.585775e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.560000e+02</td>\n", "      <td>1.338957e+04</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>2.390000e+02</td>\n", "      <td>7.487194e+04</td>\n", "      <td>1.420800e+04</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.327057e+05</td>\n", "      <td>2.146614e+05</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3.350000e+02</td>\n", "      <td>2.087215e+05</td>\n", "      <td>1.073152e+05</td>\n", "      <td>1.442584e+05</td>\n", "      <td>9.430367e+05</td>\n", "      <td>1.111909e+06</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>7.430000e+02</td>\n", "      <td>9.244552e+07</td>\n", "      <td>5.958504e+07</td>\n", "      <td>4.958504e+07</td>\n", "      <td>3.560159e+08</td>\n", "      <td>3.561793e+08</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               step        amount  oldbalanceOrg  newbalanceOrig  \\\n", "count  6.362620e+06  6.362620e+06   6.362620e+06    6.362620e+06   \n", "mean   2.433972e+02  1.798619e+05   8.338831e+05    8.551137e+05   \n", "std    1.423320e+02  6.038582e+05   2.888243e+06    2.924049e+06   \n", "min    1.000000e+00  0.000000e+00   0.000000e+00    0.000000e+00   \n", "25%    1.560000e+02  1.338957e+04   0.000000e+00    0.000000e+00   \n", "50%    2.390000e+02  7.487194e+04   1.420800e+04    0.000000e+00   \n", "75%    3.350000e+02  2.087215e+05   1.073152e+05    1.442584e+05   \n", "max    7.430000e+02  9.244552e+07   5.958504e+07    4.958504e+07   \n", "\n", "       oldbalanceDest  newbalanceDest       isFraud  isFlaggedFraud  \n", "count    6.362620e+06    6.362620e+06  6.362620e+06    6.362620e+06  \n", "mean     1.100702e+06    1.224996e+06  1.290820e-03    2.514687e-06  \n", "std      3.399180e+06    3.674129e+06  3.590480e-02    1.585775e-03  \n", "min      0.000000e+00    0.000000e+00  0.000000e+00    0.000000e+00  \n", "25%      0.000000e+00    0.000000e+00  0.000000e+00    0.000000e+00  \n", "50%      1.327057e+05    2.146614e+05  0.000000e+00    0.000000e+00  \n", "75%      9.430367e+05    1.111909e+06  0.000000e+00    0.000000e+00  \n", "max      3.560159e+08    3.561793e+08  1.000000e+00    1.000000e+00  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Data Quality Checks:\n", "- Negative amounts: 0\n", "- Zero amounts: 16\n", "- Fraud transactions: 8,213 (0.129%)\n"]}], "source": ["# Only proceed if dataset was loaded successfully\n", "if df is not None:\n", "    # Check for missing values\n", "    print(\"Missing values in each column:\")\n", "    missing_values = df.isnull().sum()\n", "    display(missing_values)\n", "    \n", "    if missing_values.sum() == 0:\n", "        print(\"✓ No missing values found!\")\n", "    else:\n", "        print(\"⚠ Missing values detected - will need to handle these\")\n", "    \n", "    # Check for duplicate transactions\n", "    duplicates = df.duplicated().sum()\n", "    print(f\"\\nNumber of duplicate rows: {duplicates}\")\n", "    if duplicates == 0:\n", "        print(\"✓ No duplicate transactions found!\")\n", "    else:\n", "        print(\"⚠ Duplicate transactions detected - will need to handle these\")\n", "    \n", "    # Check data types and basic statistics\n", "    print(\"\\nData types of each column:\")\n", "    display(df.dtypes)\n", "    \n", "    print(\"\\nBasic statistics of numerical columns:\")\n", "    display(df.describe())\n", "    \n", "    # Check for any obvious data quality issues\n", "    print(\"\\nData Quality Checks:\")\n", "    print(f\"- Negative amounts: {(df['amount'] < 0).sum()}\")\n", "    print(f\"- Zero amounts: {(df['amount'] == 0).sum()}\")\n", "    print(f\"- Fraud transactions: {df['isFraud'].sum():,} ({df['isFraud'].mean()*100:.3f}%)\")\n", "else:\n", "    print(\"Cannot proceed with data cleaning - dataset not loaded.\")"]}, {"cell_type": "markdown", "id": "b02fee69", "metadata": {}, "source": ["## Transaction Types Analysis\n", "\n", "Let's examine the different types of transactions in our dataset and their distributions. This will help us understand the nature of the transactions and identify any patterns in the data."]}, {"cell_type": "code", "execution_count": 5, "id": "51ae9209", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transaction type distribution:\n"]}, {"data": {"text/plain": ["type\n", "CASH_OUT    2237500\n", "PAYMENT     2151495\n", "CASH_IN     1399284\n", "TRANSFER     532909\n", "DEBIT         41432\n", "Name: count, dtype: int64"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Fraud analysis by transaction type:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Fraud_Count</th>\n", "      <th>Total_Count</th>\n", "      <th>Fraud_Rate</th>\n", "      <th>Fraud_Rate_Percent</th>\n", "    </tr>\n", "    <tr>\n", "      <th>type</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>CASH_IN</th>\n", "      <td>0</td>\n", "      <td>1399284</td>\n", "      <td>0.0000</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CASH_OUT</th>\n", "      <td>4116</td>\n", "      <td>2237500</td>\n", "      <td>0.0018</td>\n", "      <td>0.18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DEBIT</th>\n", "      <td>0</td>\n", "      <td>41432</td>\n", "      <td>0.0000</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PAYMENT</th>\n", "      <td>0</td>\n", "      <td>2151495</td>\n", "      <td>0.0000</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TRANSFER</th>\n", "      <td>4097</td>\n", "      <td>532909</td>\n", "      <td>0.0077</td>\n", "      <td>0.77</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          Fraud_Count  Total_Count  Fraud_Rate  Fraud_Rate_Percent\n", "type                                                              \n", "CASH_IN             0      1399284      0.0000                0.00\n", "CASH_OUT         4116      2237500      0.0018                0.18\n", "DEBIT               0        41432      0.0000                0.00\n", "PAYMENT             0      2151495      0.0000                0.00\n", "TRANSFER         4097       532909      0.0077                0.77"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Only proceed if dataset was loaded successfully\n", "if df is not None:\n", "    # Analyze transaction types\n", "    transaction_types = df['type'].value_counts()\n", "    print(\"Transaction type distribution:\")\n", "    display(transaction_types)\n", "    \n", "    # Create a pie chart of transaction types\n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    # Create subplot for pie chart and bar chart\n", "    plt.subplot(1, 2, 1)\n", "    plt.pie(transaction_types.values, labels=transaction_types.index, autopct='%1.1f%%', startangle=90)\n", "    plt.title('Distribution of Transaction Types')\n", "    \n", "    # Create bar chart for better readability\n", "    plt.subplot(1, 2, 2)\n", "    transaction_types.plot(kind='bar', color='skyblue')\n", "    plt.title('Transaction Count by Type')\n", "    plt.xlabel('Transaction Type')\n", "    plt.ylabel('Count')\n", "    plt.xticks(rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Calculate fraud rate by transaction type\n", "    fraud_by_type = df.groupby('type')['isFraud'].agg(['sum', 'count', 'mean']).round(4)\n", "    fraud_by_type.columns = ['Fraud_Count', 'Total_Count', 'Fraud_Rate']\n", "    fraud_by_type['Fraud_Rate_Percent'] = fraud_by_type['Fraud_Rate'] * 100\n", "    \n", "    print(\"\\nFraud analysis by transaction type:\")\n", "    display(fraud_by_type)\n", "    \n", "    # Visualize fraud rates\n", "    plt.figure(figsize=(10, 6))\n", "    fraud_by_type['Fraud_Rate_Percent'].plot(kind='bar', color='red', alpha=0.7)\n", "    plt.title('Fraud Rate by Transaction Type')\n", "    plt.xlabel('Transaction Type')\n", "    plt.ylabel('Fraud Rate (%)')\n", "    plt.xticks(rotation=45)\n", "    plt.grid(axis='y', alpha=0.3)\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"Cannot proceed with transaction analysis - dataset not loaded.\")"]}, {"cell_type": "markdown", "id": "5bfd8087", "metadata": {}, "source": ["## Data Dictionary\n", "\n", "Let's create a comprehensive data dictionary to document our dataset's structure and contents. This will be useful for future reference and for the dashboard development phase."]}, {"cell_type": "code", "execution_count": 6, "id": "3e763584", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data Dictionary:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Column Name</th>\n", "      <th>Data Type</th>\n", "      <th>Non-Null Count</th>\n", "      <th>Unique Values</th>\n", "      <th>Sample Values</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>step</td>\n", "      <td>int64</td>\n", "      <td>6362620</td>\n", "      <td>743</td>\n", "      <td>[1 2 3]...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>type</td>\n", "      <td>object</td>\n", "      <td>6362620</td>\n", "      <td>5</td>\n", "      <td>['PAYMENT' 'TRANSFER' 'CASH_OUT']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>amount</td>\n", "      <td>float64</td>\n", "      <td>6362620</td>\n", "      <td>5316900</td>\n", "      <td>[9839.64 1864.28  181.  ]...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>nameOrig</td>\n", "      <td>object</td>\n", "      <td>6362620</td>\n", "      <td>6353307</td>\n", "      <td>['C1231006815' 'C1666544295' 'C1305486145']...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>oldbalanceOrg</td>\n", "      <td>float64</td>\n", "      <td>6362620</td>\n", "      <td>1845844</td>\n", "      <td>[170136.  21249.    181.]...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>newbalanceOrig</td>\n", "      <td>float64</td>\n", "      <td>6362620</td>\n", "      <td>2682586</td>\n", "      <td>[160296.36  19384.72      0.  ]...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>nameDest</td>\n", "      <td>object</td>\n", "      <td>6362620</td>\n", "      <td>2722362</td>\n", "      <td>['M1979787155' 'M2044282225' 'C553264065']...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>oldbalanceDest</td>\n", "      <td>float64</td>\n", "      <td>6362620</td>\n", "      <td>3614697</td>\n", "      <td>[    0. 21182. 41898.]...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>newbalanceDest</td>\n", "      <td>float64</td>\n", "      <td>6362620</td>\n", "      <td>3555499</td>\n", "      <td>[     0.    40348.79 157982.12]...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>is<PERSON><PERSON><PERSON></td>\n", "      <td>int64</td>\n", "      <td>6362620</td>\n", "      <td>2</td>\n", "      <td>[0 1]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>isFlaggedFraud</td>\n", "      <td>int64</td>\n", "      <td>6362620</td>\n", "      <td>2</td>\n", "      <td>[0 1]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Column Name Data Type  Non-Null Count  Unique Values  \\\n", "0             step     int64         6362620            743   \n", "1             type    object         6362620              5   \n", "2           amount   float64         6362620        5316900   \n", "3         nameOrig    object         6362620        6353307   \n", "4    oldbalanceOrg   float64         6362620        1845844   \n", "5   newbalanceOrig   float64         6362620        2682586   \n", "6         nameDest    object         6362620        2722362   \n", "7   oldbalanceDest   float64         6362620        3614697   \n", "8   newbalanceDest   float64         6362620        3555499   \n", "9          is<PERSON><PERSON><PERSON>     int64         6362620              2   \n", "10  isFlaggedFraud     int64         6362620              2   \n", "\n", "                                     Sample Values  \n", "0                                       [1 2 3]...  \n", "1                ['PAYMENT' 'TRANSFER' 'CASH_OUT']  \n", "2                     [9839.64 1864.28  181.  ]...  \n", "3   ['C1231006815' 'C1666544295' 'C1305486145']...  \n", "4                     [170136.  21249.    181.]...  \n", "5               [160296.36  19384.72      0.  ]...  \n", "6    ['M1979787155' 'M2044282225' 'C553264065']...  \n", "7                        [    0. 21182. 41898.]...  \n", "8               [     0.    40348.79 157982.12]...  \n", "9                                            [0 1]  \n", "10                                           [0 1]  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Detailed Column Descriptions:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Column</th>\n", "      <th>Description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>step</td>\n", "      <td>Time step in simulation (1 step = 1 hour)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>type</td>\n", "      <td>Transaction type (CASH_IN, CASH_OUT, DEBIT, PA...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>amount</td>\n", "      <td>Transaction amount in local currency units</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>nameOrig</td>\n", "      <td>Customer ID who initiated the transaction</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>oldbalanceOrg</td>\n", "      <td>Initial balance of sender before transaction</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>newbalanceOrig</td>\n", "      <td>New balance of sender after transaction</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>nameDest</td>\n", "      <td>Customer ID who received the transaction</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>oldbalanceDest</td>\n", "      <td>Initial balance of receiver before transaction</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>newbalanceDest</td>\n", "      <td>New balance of receiver after transaction</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>is<PERSON><PERSON><PERSON></td>\n", "      <td>Binary fraud indicator (1=fraud, 0=legitimate)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>isFlaggedFraud</td>\n", "      <td>No description available</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            Column                                        Description\n", "0             step          Time step in simulation (1 step = 1 hour)\n", "1             type  Transaction type (CASH_IN, CASH_OUT, DEBIT, PA...\n", "2           amount         Transaction amount in local currency units\n", "3         nameOrig          Customer ID who initiated the transaction\n", "4    oldbalanceOrg       Initial balance of sender before transaction\n", "5   newbalanceOrig            New balance of sender after transaction\n", "6         nameDest           Customer ID who received the transaction\n", "7   oldbalanceDest     Initial balance of receiver before transaction\n", "8   newbalanceDest          New balance of receiver after transaction\n", "9          isFraud     Binary fraud indicator (1=fraud, 0=legitimate)\n", "10  isFlaggedFraud                           No description available"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "✓ Cleaned dataset saved to 'data/cleaned_transactions.csv'\n", "✓ Data dictionary saved to 'data/data_dictionary.csv'\n", "✓ Column descriptions saved to 'data/column_descriptions.csv'\n", "\n", "=== WEEK 1 SUMMARY ===\n", "Dataset shape: (6362620, 11)\n", "Total transactions: 6,362,620\n", "Fraud transactions: 8,213 (0.129%)\n", "Transaction types: 5\n", "Unique customers: 9075669\n", "Time period: 1 to 743 hours\n"]}], "source": ["# Only proceed if dataset was loaded successfully\n", "if df is not None:\n", "    # Create a comprehensive data dictionary\n", "    data_dict = {\n", "        'Column Name': df.columns,\n", "        'Data Type': df.dtypes.values,\n", "        'Non-Null Count': df.count().values,\n", "        'Unique Values': [df[col].nunique() for col in df.columns],\n", "        'Sample Values': [str(df[col].unique()[:3]) if df[col].nunique() <= 10 else str(df[col].unique()[:3]) + '...' for col in df.columns]\n", "    }\n", "    \n", "    data_dictionary = pd.DataFrame(data_dict)\n", "    print(\"Data Dictionary:\")\n", "    display(data_dictionary)\n", "    \n", "    # Create detailed column descriptions\n", "    column_descriptions = {\n", "        'step': 'Time step in simulation (1 step = 1 hour)',\n", "        'type': 'Transaction type (CASH_IN, CASH_OUT, DEBIT, PAYMENT, TRANSFER)',\n", "        'amount': 'Transaction amount in local currency units',\n", "        'nameOrig': 'Customer ID who initiated the transaction',\n", "        'nameDest': 'Customer ID who received the transaction',\n", "        'oldbalanceOrg': 'Initial balance of sender before transaction',\n", "        'newbalanceOrig': 'New balance of sender after transaction',\n", "        'oldbalanceDest': 'Initial balance of receiver before transaction',\n", "        'newbalanceDest': 'New balance of receiver after transaction',\n", "        'isFraud': 'Binary fraud indicator (1=fraud, 0=legitimate)'\n", "    }\n", "    \n", "    detailed_dict = pd.DataFrame([\n", "        {'Column': col, 'Description': column_descriptions.get(col, 'No description available')}\n", "        for col in df.columns\n", "    ])\n", "    \n", "    print(\"\\nDetailed Column Descriptions:\")\n", "    display(detailed_dict)\n", "    \n", "    # Ensure data directory exists\n", "    os.makedirs('../data', exist_ok=True)\n", "    \n", "    # Save the clean dataset\n", "    df.to_csv('../data/cleaned_transactions.csv', index=False)\n", "    print(\"\\n✓ Cleaned dataset saved to 'data/cleaned_transactions.csv'\")\n", "    \n", "    # Save data dictionary as well\n", "    data_dictionary.to_csv('../data/data_dictionary.csv', index=False)\n", "    detailed_dict.to_csv('../data/column_descriptions.csv', index=False)\n", "    print(\"✓ Data dictionary saved to 'data/data_dictionary.csv'\")\n", "    print(\"✓ Column descriptions saved to 'data/column_descriptions.csv'\")\n", "    \n", "    # Print summary statistics\n", "    print(f\"\\n=== WEEK 1 SUMMARY ===\")\n", "    print(f\"Dataset shape: {df.shape}\")\n", "    print(f\"Total transactions: {len(df):,}\")\n", "    print(f\"Fraud transactions: {df['isFraud'].sum():,} ({df['isFraud'].mean()*100:.3f}%)\")\n", "    print(f\"Transaction types: {df['type'].nunique()}\")\n", "    print(f\"Unique customers: {df['nameOrig'].nunique() + df['nameDest'].nunique()}\")\n", "    print(f\"Time period: {df['step'].min()} to {df['step'].max()} hours\")\n", "else:\n", "    print(\"Cannot create data dictionary - dataset not loaded.\")"]}, {"cell_type": "markdown", "id": "4480503c", "metadata": {}, "source": ["## Summary of Week 1 Findings\n", "\n", "### ✅ Completed Tasks:\n", "1. **Environment Setup**: Configured Python environment with all required packages\n", "2. **Data Loading**: Successfully loaded the PaySim mobile money dataset\n", "3. **Data Quality Assessment**: Checked for missing values, duplicates, and data consistency\n", "4. **Initial Analysis**: Analyzed transaction types and fraud distributions\n", "5. **Data Dictionary**: Created comprehensive documentation of dataset structure\n", "6. **Data Export**: Saved cleaned dataset and documentation for future use\n", "\n", "### 📊 Key Findings:\n", "- Dataset contains millions of synthetic mobile money transactions\n", "- 5 transaction types: CASH_IN, CASH_OUT, DEBIT, PAYMENT, TRANSFER\n", "- Fraud rate varies significantly by transaction type\n", "- No missing values or duplicate transactions detected\n", "- Data spans multiple time periods (steps) representing hours\n", "\n", "### 📁 Deliverables Created:\n", "- `data/cleaned_transactions.csv` - Clean dataset ready for analysis\n", "- `data/data_dictionary.csv` - Technical data dictionary\n", "- `data/column_descriptions.csv` - Business-friendly column descriptions\n", "- This notebook with complete Week 1 analysis\n", "\n", "### 🎯 Next Steps (Week 2):\n", "- **Exploratory Data Analysis (EDA)**: Deep dive into transaction patterns\n", "- **Time Series Analysis**: Analyze trends over time\n", "- **Fraud Pat<PERSON> Analysis**: Identify characteristics of fraudulent transactions\n", "- **Statistical Insights**: Generate key metrics for dashboard\n", "- **Visualization Creation**: Build charts for EDA report\n", "\n", "### 🚀 How to Run This Notebook:\n", "1. Ensure you have the PaySim dataset CSV file in the project directory\n", "2. Install required packages: `pip install -r requirements.txt`\n", "3. Run all cells in order\n", "4. Check the `data/` directory for output files\n", "\n", "**Ready for Week 2!** 🎉"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}